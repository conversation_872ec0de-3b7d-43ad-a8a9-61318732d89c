import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import ColorGrid from '@/components/ui/ColorGrid';
import StyleGrid from '@/components/ui/StyleGrid';
import { TagInput } from '@/components/ui/TagInput';
import Input from '@/components/ui/Input'; // Import the Input component
import { brandSuggestions } from '@/constants/suggestions';
import { colorOptions, styleOptions } from '@/constants/profileOptions'; // Import options from new file
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface PreferencesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const PreferencesSection: React.FC<PreferencesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Favorite Color */}
      <View>
        <Controller
          control={control}
          name="preferences.favoriteColor" // Updated name
          render={({ field: { onChange, value } }) => (
            <ColorGrid
              options={colorOptions}
              selectedValue={value}
              onValueChange={onChange}
              label="Favorite Color"
              error={errors.preferences?.favoriteColor?.message} // Updated error path
              accessibilityLabel="Select favorite color grid"
            />
          )}
        />
      </View>
      {/* Preferred Style */}
      <View>
        <Controller
          control={control}
          name="preferences.preferredStyle" // Updated name
          render={({ field: { onChange, value } }) => (
            <StyleGrid
              options={styleOptions}
              selectedValue={value}
              onValueChange={onChange}
              label="Preferred Style"
              error={errors.preferences?.preferredStyle?.message} // Updated error path
              accessibilityLabel="Select preferred style grid"
            />
          )}
        />
      </View>
      {/* Favorite Brands */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Favorite Brands</Text>
        <Controller
          control={control}
          name="preferences.favoriteBrands" // Updated name
          render={({ field: { onChange, value } }) => (
            <TagInput
              tags={value || []} // TagInput expects string[], value is string[] | undefined
              onChangeTags={onChange} // onChange expects string[]
              placeholder="e.g., Nike, Apple, Moleskine"
              error={errors.preferences?.favoriteBrands?.message} // Updated error path
              accessibilityLabel="Favorite brands input"
              suggestions={brandSuggestions}
            />
          )}
        />
      </View>
      {/* Minimum Budget */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Minimum Budget (Optional)</Text>
        <Controller
          control={control}
          name="preferences.budgetMin"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 50"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()} // Input expects string value
              error={errors.preferences?.budgetMin?.message}
              accessibilityLabel="Minimum budget input"
            />
          )}
        />
      </View>
      {/* Maximum Budget */}
      <View>
        <Text className="mb-1 text-sm font-medium text-muted-foreground">Maximum Budget (Optional)</Text>
        <Controller
          control={control}
          name="preferences.budgetMax"
          render={({ field: { onChange, value } }) => (
            <Input
              placeholder="e.g., 200"
              keyboardType="numeric"
              onChangeText={onChange}
              value={value?.toString()} // Input expects string value
              error={errors.preferences?.budgetMax?.message}
              accessibilityLabel="Maximum budget input"
            />
          )}
        />
      </View>
    </View>
  );
};

export default PreferencesSection;