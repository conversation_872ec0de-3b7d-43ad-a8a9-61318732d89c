import React from 'react';
import { View } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import SizeGrid from '@/components/ui/SizeGrid';
import { clothingSizeOptions, shoeSizeOptions } from '@/constants/profileOptions';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface SizesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const SizesSection: React.FC<SizesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-3">
      {/* Clothing Size */}
      <View>
        <Controller
          control={control}
          name="clothingSize"
          render={({ field: { onChange, value } }) => (
            <SizeGrid
              options={clothingSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              label="Clothing Size"
              error={errors.clothingSize?.message}
              accessibilityLabel="Select clothing size grid"
              sizeType="clothing"
            />
          )}
        />
      </View>
      {/* Shoe Size */}
      <View>
        <Controller
          control={control}
          name="shoeSize"
          render={({ field: { onChange, value } }) => (
            <SizeGrid
              options={shoeSizeOptions}
              selectedValue={value}
              onValueChange={onChange}
              label="Shoe Size"
              error={errors.shoeSize?.message}
              accessibilityLabel="Select shoe size grid"
              sizeType="shoe"
            />
          )}
        />
      </View>
    </View>
  );
};

export default SizesSection;