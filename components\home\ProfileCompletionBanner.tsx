import React, { useEffect, useMemo } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { useRouter } from 'expo-router';

import { SignificantOtherProfile } from '../../functions/src/types/firestore';

interface ProfileCompletionBannerProps {
  profile: SignificantOtherProfile | null;
  className?: string;
}

// Use the same calculation as the profile detail page
const calculateProfileStrength = (profile: SignificantOtherProfile | null): number => {
  if (!profile) return 0;
  let strength = 0;
  const MAX_ITEMS_FOR_POINTS = 3;
  const POINTS_PER_ITEM_ARRAY = 4;
  const POINTS_PER_SIMPLE_FIELD = 5;
  const POINTS_PER_PREFERENCE = 7;

  if (profile.birthday) strength += POINTS_PER_SIMPLE_FIELD;
  if (profile.anniversary) strength += POINTS_PER_SIMPLE_FIELD;
  strength += Math.min(profile.interests?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  strength += Math.min(profile.dislikes?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  if (profile.preferences?.favoriteColor) strength += POINTS_PER_PREFERENCE;
  if (profile.preferences?.preferredStyle) strength += POINTS_PER_PREFERENCE;
  // Award points for budget only if both min and max are present or a significant one is
  if (profile.preferences?.budgetMin && profile.preferences?.budgetMax) strength += POINTS_PER_PREFERENCE;
  else if (profile.preferences?.budgetMin || profile.preferences?.budgetMax) strength += Math.floor(POINTS_PER_PREFERENCE / 2);

  strength += Math.min(profile.preferences?.favoriteBrands?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  if (profile.sizes?.clothing || profile.sizes?.shoe) strength += POINTS_PER_PREFERENCE;

  strength += Math.min(profile.wishlistItems?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  strength += Math.min(profile.pastGiftsGiven?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  strength += Math.min(profile.generalNotes?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
  strength += Math.min(profile.customDates?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;

  return Math.min(Math.round(strength), 100);
};

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({
  profile,
  className = ''
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();

  const profileStrength = useMemo(() => calculateProfileStrength(profile), [profile]);
  const animatedStrength = useSharedValue(0);

  useEffect(() => {
    animatedStrength.value = withSpring(profileStrength, { damping: 15, stiffness: 100 });
  }, [profileStrength]);

  const strengthIndicatorStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      ['#A3002B', '#C70039', '#A3002B', '#800022'] // Consistent red theme colors
    );
    return { width: `${animatedStrength.value}%`, backgroundColor: color };
  });

  const strengthTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      ['#A3002B', '#C70039', '#A3002B', '#800022'] // Consistent red theme colors
    );
    return { color };
  });

  // Only show banner if completion is below 50%
  if (!profile || profileStrength >= 50) {
    return null;
  }

  const handleCompleteProfile = () => {
    if (profile) {
      router.push(`/profiles/${profile.profileId}/edit`);
    }
  };

  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      className={`mb-6 ${className}`}
    >
      <View className="p-4 rounded-xl bg-accent/10 dark:bg-accent-dark/10 border border-accent/30 dark:border-accent-dark/30">
        <View className="flex-row items-start">
          <View className="p-2 rounded-full mr-3 bg-accent/20 dark:bg-accent-dark/20">
            <Feather
              name="alert-triangle"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
          </View>

          <View className="flex-1">
            <View className="flex-row items-center justify-between mb-1">
              <Text className="text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                Profile Strength
              </Text>
              <Animated.Text style={strengthTextStyle} className="text-sm font-bold">
                {profileStrength}%
              </Animated.Text>
            </View>

            {/* Animated Progress Bar */}
            <View className="mb-3">
              <View className={`w-full h-2.5 rounded-full overflow-hidden ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`}>
                <Animated.View style={strengthIndicatorStyle} className="h-full rounded-full" />
              </View>
            </View>

            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mb-3 leading-relaxed">
              Complete {profile.name}'s profile to get better gift recommendations.
              Add more details like interests, preferences, and important dates.
            </Text>

            <TouchableOpacity
              onPress={handleCompleteProfile}
              className="flex-row items-center justify-center py-2 px-4 rounded-lg bg-accent dark:bg-accent-dark active:opacity-80"
            >
              <Feather
                name="edit-3"
                size={16}
                color="white"
                style={{ marginRight: 8 }}
              />
              <Text className="text-sm font-medium text-white">
                Complete Profile
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

export default ProfileCompletionBanner;
