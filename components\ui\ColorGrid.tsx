import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import ColorModal from './ColorModal';

interface ColorOption {
  label: string;
  value: string;
  color: string;
}

interface ColorGridProps {
  options: ColorOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  label?: string;
  error?: string;
  accessibilityLabel?: string;
}



const ColorGrid: React.FC<ColorGridProps> = ({
  options,
  selectedValue,
  onValueChange,
  label,
  error,
  accessibilityLabel,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    error: isDark ? '#F87171' : '#EF4444',
  };

  // Find selected color option
  const selectedOption = options.find(option => option.value === selectedValue);

  const openModal = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const handleColorSelect = (value: string) => {
    onValueChange(value);
    closeModal();
  };

    return (
    <View className="mb-4">
      {/* Label */}
      {label && (
        <Text
          className="mb-3 text-sm font-medium"
          style={{ color: themedColors.textSecondary }}
        >
          {label}
        </Text>
      )}

      {/* Trigger Button */}
      <TouchableOpacity
        onPress={openModal}
        className="flex-row justify-between items-center p-4 rounded-xl border"
        style={{
          backgroundColor: themedColors.card,
          borderColor: error ? themedColors.error : themedColors.border,
        }}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || "Select color"}
      >
        <View className="flex-row flex-1 items-center">
          {/* Selected Color Preview */}
          {selectedOption ? (
            <>
              <View
                className="mr-3 w-8 h-8 rounded-full border-2"
                style={{
                  backgroundColor: selectedOption.color === '#f8fafc' && !isDark ? '#f1f5f9' : selectedOption.color,
                  borderColor: selectedOption.color === '#f8fafc' || selectedOption.label === 'White' 
                    ? themedColors.border 
                    : 'transparent',
                }}
              />
              <Text
                className="text-base font-medium"
                style={{ color: themedColors.textPrimary }}
              >
                {selectedOption.label}
              </Text>
            </>
          ) : (
            <Text
              className="text-base"
              style={{ color: themedColors.textSecondary }}
            >
              Select Color
            </Text>
          )}
        </View>

        {/* Chevron Icon */}
        <Feather
          name="chevron-down"
          size={20}
          color={themedColors.textSecondary}
        />
      </TouchableOpacity>

      {/* Color Modal */}
      <ColorModal
        isVisible={isModalVisible}
        onClose={closeModal}
        options={options}
        selectedValue={selectedValue}
        onValueChange={handleColorSelect}
        title={label || "Choose Color"}
      />

      {/* Error Message */}
      {error && (
        <Text
          className="mt-2 text-sm"
          style={{ color: themedColors.error }}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default ColorGrid; 