import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import StyleModal from './StyleModal';

interface StyleOption {
  label: string;
  value: string;
  description: string;
  icon: string;
  category: string;
}

interface StyleGridProps {
  options: StyleOption[];
  selectedValue?: string;
  onValueChange: (value: string) => void;
  label?: string;
  error?: string;
  accessibilityLabel?: string;
}

const StyleGrid: React.FC<StyleGridProps> = ({
  options,
  selectedValue,
  onValueChange,
  label,
  error,
  accessibilityLabel,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Theme colors
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    border: isDark ? '#374151' : '#E5E7EB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    background: isDark ? '#111827' : '#F9FAFB',
    error: isDark ? '#F87171' : '#EF4444',
  };

  // Find selected style option
  const selectedOption = options.find(option => option.value === selectedValue);

  const openModal = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const handleStyleSelect = (value: string) => {
    onValueChange(value);
    closeModal();
  };

  return (
    <View className="mb-4">
      {/* Label */}
      {label && (
        <Text
          className="mb-3 text-sm font-medium"
          style={{ color: themedColors.textSecondary }}
        >
          {label}
        </Text>
      )}

      {/* Trigger Button */}
      <TouchableOpacity
        onPress={openModal}
        className="flex-row justify-between items-center p-4 rounded-xl border"
        style={{
          backgroundColor: themedColors.card,
          borderColor: error ? themedColors.error : themedColors.border,
        }}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || "Select style preference"}
      >
        <View className="flex-row flex-1 items-center">
          {/* Selected Style Preview */}
          {selectedOption ? (
            <>
              <View
                className="mr-4 p-2 rounded-full"
                style={{
                  backgroundColor: themedColors.background,
                }}
              >
                <Feather
                  name={selectedOption.icon as any}
                  size={20}
                  color={themedColors.primary}
                />
              </View>
              <View className="flex-1">
                <Text
                  className="text-base font-medium"
                  style={{ color: themedColors.textPrimary }}
                >
                  {selectedOption.label}
                </Text>
                <Text
                  className="text-sm mt-1"
                  style={{ color: themedColors.textSecondary }}
                  numberOfLines={1}
                >
                  {selectedOption.description}
                </Text>
              </View>
            </>
          ) : (
            <Text
              className="text-base"
              style={{ color: themedColors.textSecondary }}
            >
              Select Style Preference
            </Text>
          )}
        </View>

        {/* Chevron Icon */}
        <Feather
          name="chevron-down"
          size={20}
          color={themedColors.textSecondary}
        />
      </TouchableOpacity>

      {/* Style Modal */}
      <StyleModal
        isVisible={isModalVisible}
        onClose={closeModal}
        options={options}
        selectedValue={selectedValue}
        onValueChange={handleStyleSelect}
        title={label || "Choose Style Preference"}
      />

      {/* Error Message */}
      {error && (
        <Text
          className="mt-2 text-sm"
          style={{ color: themedColors.error }}
        >
          {error}
        </Text>
      )}
    </View>
  );
};

export default StyleGrid; 